<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offerte Generator Pro</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    

    
    <style>
        /* --- MODERNE PROFESSIONELE THEME KLEUREN --- */
        :root {
            --primary-color: #0066cc;
            --primary-hover: #0052a3;
            --secondary-color: #6c5ce7;
            --accent-color: #00b894;
            --dark-color: #2d3436;
            --darker-color: #1e2124;
            --light-gray: #f1f3f4;
            --lighter-gray: #fafbfc;
            --text-color: #2d3436;
            --text-light: #636e72;
            --border-color: #ddd;
            --form-bg: #ffffff;
            --app-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --danger-color: #e17055;
            --success-color: #00b894;
            --warning-color: #fdcb6e;
            --shadow-light: 0 2px 8px rgba(0,0,0,0.04);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.08);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.12);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
            --gradient-accent: linear-gradient(135deg, #00b894 0%, #55efc4 100%);
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
        }

        /* --- ALGEMENE STIJL APPLICATIE --- */
        *, *::before, *::after { box-sizing: border-box; }

        body {
            font-family: 'Inter', 'Segoe UI', 'Open Sans', sans-serif;
            background: var(--app-bg);
            margin: 0;
            color: var(--text-color);
            line-height: 1.6;
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            overflow-x: hidden;
        }
        

        

        
        .app-header {
            background: linear-gradient(135deg, var(--darker-color) 0%, var(--dark-color) 100%);
            color: white;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--shadow-heavy), 0 1px 0 rgba(255,255,255,0.1) inset;
            z-index: 100;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255,255,255,0.1);
            position: relative;
        }
        .app-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-primary);
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
        }
        .app-header h1 { 
            font-family: 'Inter', sans-serif; 
            margin: 0; 
            font-size: 1.75rem; 
            font-weight: 700;
            background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.5px;
        }
        .app-header .actions button {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-left: 12px;
            box-shadow: var(--shadow-medium);
            position: relative;
            overflow: hidden;
        }
        .app-header .actions button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        .app-header .actions button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: var(--shadow-heavy);
        }
        .app-header .actions button:hover::before {
            left: 100%;
        }
        .app-header .actions button#settings-btn {
            background: var(--gradient-secondary);
        }

        .main-container {
            display: grid;
            grid-template-columns: 900px 1fr;
            gap: 30px;
            padding: 30px;
            min-height: calc(100vh - 120px);
            position: relative;
        }
        .main-container::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--app-bg);
            z-index: -1;
        }

        .form-container, .preview-container {
            border-radius: 20px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-heavy), 0 1px 0 rgba(255,255,255,0.2) inset;
            overflow-y: auto;
            max-height: calc(100vh - 140px);
            position: relative;
        }
        .form-container::before, .preview-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gradient-primary);
            border-radius: 20px 20px 0 0;
        }
        
        /* --- FORMULIER STIJL --- */
        .form-container {
            padding: 25px 15px 25px 35px;
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
        }
        .form-section {
            margin-bottom: 35px;
            border-left: 4px solid transparent;
            border-image: var(--gradient-primary) 1;
            padding-left: 25px;
            position: relative;
        }
        .form-section::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--gradient-primary);
            border-radius: 2px;
        }
        .form-section h2 {
            font-family: 'Inter', sans-serif;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--dark-color);
            margin: 0 0 25px 0;
            display: flex;
            align-items: center;
            gap: 12px;
            letter-spacing: -0.3px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .form-section h2 svg { width: 20px; height: 20px; fill: var(--primary-color); }
        .form-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
        .form-group { display: flex; flex-direction: column; gap: 5px; }
        label {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 8px;
            display: block;
            letter-spacing: 0.3px;
        }
        input[type="text"], input[type="date"], input[type="email"], input[type="number"], textarea {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 0.95rem;
            font-family: 'Inter', sans-serif;
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-light);
        }
        input:focus, textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15), var(--shadow-medium);
            transform: translateY(-2px);
            background: rgba(255,255,255,1);
        }
        input:hover, textarea:hover {
            border-color: var(--primary-hover);
            box-shadow: var(--shadow-medium);
        }
        textarea { resize: vertical; min-height: 80px; }
        .full-width { grid-column: 1 / -1; }

        #items-form-container .item-row { background-color: #fdfdfd; border: 1px solid var(--light-gray); border-radius: 6px; padding: 15px; margin-bottom: 15px; }
        .item-row-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .item-row-header strong { font-family: 'Montserrat', sans-serif; color: var(--dark-color); }
        .item-row .remove-item-btn { background: none; border: none; color: var(--border-color); font-size: 1.5rem; cursor: pointer; padding: 0 5px; transition: color 0.2s; }
        .item-row .remove-item-btn:hover { color: var(--danger-color); }
        #add-item-btn {
            width: 100%; padding: 12px; background-color: #fff; color: var(--primary-color);
            border: 2px dashed var(--primary-color); border-radius: 6px; cursor: pointer;
            font-weight: 600; font-size: 1rem; margin-top: 10px; transition: all 0.2s;
        }
        #add-item-btn:hover { background-color: var(--primary-color); color: white; }

        /* --- MODERNE PREVIEW STIJL --- */
        .preview-container {
            padding: 25px;
            background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,250,252,0.9) 100%);
            backdrop-filter: blur(20px);
        }
        #quote-preview {
            transform: scale(0.96);
            transform-origin: top center;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        #quote-preview:hover {
            transform: scale(0.98);
        }
        
        /* --- MODERNE OFFERTE SJABLOON --- */
        .quote-container {
            max-width: 870px;
            margin: 15px auto;
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            box-shadow: var(--shadow-heavy), 0 0 0 1px rgba(255,255,255,0.8) inset;
            border-radius: 20px;
            overflow: hidden;
            font-family: 'Inter', sans-serif;
            color: var(--text-color);
            font-size: 15px;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
            border: 1px solid rgba(0,0,0,0.05);
            position: relative;
        }
        .quote-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            border-radius: 20px 20px 0 0;
        }
        .quote-header {
            display: flex;
            justify-content: space-between;
            background: linear-gradient(135deg, var(--darker-color) 0%, var(--dark-color) 100%);
            color: #fff;
            position: relative;
            overflow: hidden;
        }
        .quote-header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.05) 50%, transparent 70%);
            pointer-events: none;
        }
        .logo-area {
            padding: 25px 30px;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            backdrop-filter: blur(10px);
        }
        .logo-area img {
            max-height: 50px;
            max-width: 180px;
            object-fit: contain;
            background: none !important;
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            filter: brightness(0) invert(1);
        }
        .title-area {
            background: var(--gradient-primary);
            padding: 25px 35px;
            clip-path: polygon(15% 0, 100% 0, 100% 100%, 0% 100%);
            min-width: 280px;
            text-align: right;
            position: relative;
            box-shadow: -10px 0 20px rgba(102, 126, 234, 0.3);
        }
        .title-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 50%);
            pointer-events: none;
        }
        .title-area h1 {
            margin: 0;
            font-family: 'Inter', sans-serif;
            font-size: 2.4em;
            font-weight: 800;
            letter-spacing: -1px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            background: linear-gradient(135deg, #ffffff 0%, rgba(255,255,255,0.8) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .meta-info, .totals-section, .quote-footer {
            background: linear-gradient(135deg, #f8fafb 0%, #ecf0f1 100%);
        }
        .meta-info {
            display: flex;
            justify-content: space-around;
            padding: 15px 25px;
            border-bottom: 1px solid rgba(0,0,0,0.08);
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,251,0.9) 100%);
            backdrop-filter: blur(10px);
        }
        .meta-block {
            text-align: center;
            padding: 8px 12px;
            border-radius: 8px;
            background: rgba(255,255,255,0.6);
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }
        .meta-block:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-light);
        }
        .meta-block span {
            font-size: 0.8em;
            color: var(--text-light);
            font-weight: 500;
        }
        .meta-block strong {
            display: block;
            font-size: 0.95em;
            color: var(--dark-color);
            font-weight: 700;
            margin-top: 4px;
        }
        .parties-info {
          display: flex;
          flex-direction: row;
          gap: 22px;
          padding: 20px 25px;
          border-bottom: 1px solid #ecf0f1;
          justify-content: space-between;
        }
        .info-box h3 { margin: 0 0 8px 0; font-family: 'Montserrat', sans-serif; font-size: 0.8em; color: var(--primary-color); border-bottom: 2px solid var(--primary-color); padding-bottom: 4px; letter-spacing: 1px; text-transform: uppercase; }
        .info-box p { margin: 0; line-height: 1.5; font-size: 0.85em; white-space: pre-wrap; }
        .quote-main { padding: 20px 25px; } .quote-main h3 { font-family: 'Montserrat', sans-serif; color: #2c3e50; margin: 0 0 15px 0; font-size: 1.1rem; }
        .item-table { 
          width: 100%; 
          border-collapse: collapse; 
          border-radius: 8px; 
          overflow: hidden; 
          box-shadow: 0 2px 12px rgba(22,160,133,0.08);
        }
        .item-table thead { 
          background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); 
          color: #fff; 
        }
        .item-table th { 
          padding: 12px 10px; 
          text-align: left; 
          font-weight: 700; 
          font-size: 0.75em; 
          text-transform: uppercase; 
          letter-spacing: 0.5px;
          border-right: 1px solid rgba(255,255,255,0.1);
        }
        .item-table th:last-child {
          border-right: none;
        }
        .item-table tbody tr { 
          border-bottom: 1px solid #ecf0f1; 
          page-break-inside: avoid; 
          transition: background-color 0.2s ease;
        }
        .item-table tbody tr:hover {
          background-color: rgba(22,160,133,0.02);
        }
        .item-table tbody tr:nth-child(even) {
          background-color: rgba(248,250,251,0.5);
        }
        .item-table td { 
          padding: 8px 10px; 
          vertical-align: top; 
          font-size: 0.85em;
        } 
        .item-table .description { 
          font-size: 0.75em; 
          color: #666; 
          margin: 3px 0 0 0; 
          white-space: pre-wrap; 
          font-style: italic;
        }
        .item-table th:nth-child(n+2), .item-table td:nth-child(n+2) { text-align: right; }
        .totals-section { 
          display: flex; 
          justify-content: space-between; 
          align-items: flex-start; 
          padding: 16px 20px; 
          background: linear-gradient(135deg, #f8fafb 0%, #ecf0f1 100%); 
          border-bottom: 1px solid #ddd; 
          page-break-inside: avoid;
          border-radius: 8px;
          margin: 8px 0;
          box-shadow: 0 2px 8px rgba(22,160,133,0.05);
        }
        .notes { 
          width: 55%; 
          background: #ffffff;
          padding: 12px;
          border-radius: 6px;
          border: 1px solid rgba(22,160,133,0.1);
        } 
        .notes h4 { 
          margin: 0 0 6px 0; 
          font-family: 'Montserrat', sans-serif; 
          color: #2c3e50; 
          font-size: 0.9em;
          font-weight: 600;
        } 
        .notes p { 
          font-size: 0.75em; 
          color: #555; 
          margin: 0; 
          white-space: pre-wrap; 
          line-height: 1.4;
        }
        .totals-summary { 
          width: 40%; 
          font-size: 10px !important;
          background: #ffffff;
          padding: 12px;
          border-radius: 6px;
          border: 1px solid rgba(22,160,133,0.1);
        }
        .totals-table { 
          width: 100%; 
          font-size: 0.8em !important; 
        }
        .totals-table td { 
          padding: 3px 0 !important; 
          text-align: right; 
        }
        .totals-table td:first-child { 
          text-align: left; 
          font-weight: 500;
        }
        .grand-total { 
          font-weight: bold; 
          font-size: 1.1em; 
        } 
        .grand-total td { 
          padding-top: 8px; 
          border-top: 2px solid #16a085; 
        }
        .grand-total td:last-child { 
          font-family: 'Montserrat', sans-serif; 
          color: #16a085; 
          font-size: 1.2em; 
          font-weight: 700;
        }
        .details-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px 25px; padding: 20px 25px; border-bottom: 1px solid #ecf0f1; page-break-inside: avoid; }
        .detail-block h4 { font-family: 'Montserrat', sans-serif; color: #2c3e50; margin: 0 0 8px 0; font-size: 0.9em; } .detail-block ul { margin: 0; padding-left: 15px; font-size: 0.85em; line-height: 1.6; } .detail-block p { font-size: 0.85em; line-height: 1.5; margin: 0; white-space: pre-wrap; }
        .detail-block h4:first-line { color: var(--primary-color); }
        .acceptance-section { 
          padding: 16px 20px; 
          page-break-inside: avoid;
          background: linear-gradient(135deg, #ffffff 0%, #f8fafb 100%);
          border-radius: 8px;
          margin: 8px 0;
          border: 1px solid rgba(22,160,133,0.1);
          box-shadow: 0 2px 8px rgba(22,160,133,0.05);
        } 
        .acceptance-section h3 { 
          font-family: 'Montserrat', sans-serif; 
          color: #2c3e50; 
          margin: 0 0 8px 0; 
          font-size: 1em;
          font-weight: 600;
          background: linear-gradient(90deg, #16a085, #1abc9c);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        } 
        .acceptance-section p { 
          font-size: 0.8em; 
          margin: 0;
          line-height: 1.4;
          color: #555;
        }
        .signature-area { 
          display: grid; 
          grid-template-columns: 1fr 1fr 1fr; 
          gap: 16px; 
          margin-top: 16px; 
        } 
        .signature-block { 
          border-top: 2px solid #16a085; 
          padding-top: 6px; 
          font-size: 0.75em; 
          color: #555;
          text-align: center;
          background: rgba(22,160,133,0.02);
          padding: 8px 4px 4px 4px;
          border-radius: 4px;
        }
        .quote-footer { 
          padding: 12px 20px; 
          text-align: center; 
          font-size: 0.75em; 
          color: #888; 
          background: linear-gradient(135deg, #ecf0f1 0%, #d5dbdb 100%);
          border-radius: 8px;
          margin: 8px 0;
        }
        
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.6); }
        .modal-content { background-color: #fefefe; margin: 10% auto; padding: 30px; border: 1px solid #888; width: 80%; max-width: 600px; border-radius: 8px; box-shadow: 0 5px 20px rgba(0,0,0,0.3); position: relative; }
        .close-btn { color: #aaa; position: absolute; top: 15px; right: 25px; font-size: 28px; font-weight: bold; cursor: pointer; }
        .close-btn:hover, .close-btn:focus { color: black; text-decoration: none; }
        
        /* Moderne Info Cards Hover Effecten */
        .modern-info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.08) !important;
        }
        
        .modern-quote-info .modern-info-card:last-child:hover {
            box-shadow: 0 6px 20px rgba(22,160,133,0.25) !important;
        }
        
        /* Compacte styling alleen voor schermweergave */
        @media screen {
            .quote-main {
                padding: 12px 16px !important;
            }
            .quote-main h3 {
                margin: 0 0 8px 0 !important;
                font-size: 1rem !important;
            }
            .totals-section {
                padding: 12px 16px !important;
            }
            .totals-section .notes h4 {
                margin: 0 0 6px 0 !important;
                font-size: 0.9rem !important;
            }
            .totals-section .notes p {
                font-size: 0.8rem !important;
                line-height: 1.3 !important;
            }
            .totals-table {
                font-size: 0.85rem !important;
            }
            .totals-table td {
                padding: 2px 0 !important;
            }
            .totals-table .grand-total td {
                padding: 4px 0 0 0 !important;
            }
            .acceptance-section {
                padding: 12px 16px !important;
            }
            .acceptance-section h3 {
                margin: 0 0 6px 0 !important;
                font-size: 1rem !important;
            }
            .acceptance-section p {
                margin: 0 0 8px 0 !important;
                font-size: 0.85rem !important;
            }
            .signature-area {
                gap: 8px !important;
            }
            .signature-block {
                padding: 8px !important;
                font-size: 0.8rem !important;
            }
            .quote-footer {
                padding: 8px 16px !important;
            }
            .quote-footer p {
                margin: 0 !important;
                font-size: 0.8rem !important;
            }
        }
        
        @page { size: A4; margin: 10mm; }
        @media print {
            body > *:not(.main-container) { display: none; }
            .main-container > *:not(.preview-container) { display: none; }
            body, .main-container { padding: 0; margin: 0; background: #fff; }
            .preview-container { padding: 0; margin: 0; box-shadow: none; max-height: none; background: #fff; }
            #quote-preview { transform: none; }
            .quote-container { max-width: 100%; margin: 0; box-shadow: none; border: none; font-size: 13.5px; }
            .logo-area img { max-height: 40px; }
            .title-area h1 { font-size: 2em; }
            .logo-area, .title-area, .parties-info, .quote-main, .totals-section, .details-grid, .acceptance-section { padding: 15px 20px; }
            .item-table td, .item-table th { padding: 8px 10px; }
            .info-box p, .detail-block ul, .detail-block p { font-size: 0.9em; }
            .grand-total td:last-child { font-size: 1.2em; }
            .quote-main h3 { margin-bottom: 10px; }
        }
        
        /* Compacte styling voor moderne info sectie alleen */
        @media screen {
            .parties-info {
                padding: 8px !important;
                margin: 8px 0 !important;
            }
            .info-box {
                padding: 8px !important;
                border-radius: 4px !important;
            }
            .info-box h3 {
                font-size: 12.75px !important;
                margin-bottom: 6px !important;
                letter-spacing: 0.2px !important;
            }
            .info-box p {
                font-size: 12.75px !important;
                line-height: 1.3 !important;
            }
            .info-box p strong,
            .info-box p span {
                font-size: 12.75px !important;
            }
            .offerte-details-box > div {
                padding: 6px 10px !important;
                font-size: 12.75px !important;
            }
            .offerte-details-box > div > div {
                padding: 1px 0 !important;
            }
            .offerte-details-box > div > div span,
            .offerte-details-box > div > div strong {
                font-size: 12.75px !important;
            }
            #preview-verzender-info {
                font-size: 12.75px !important;
                line-height: 1.3 !important;
            }
        }
        
        @media (max-width: 992px) { 
            .main-container { grid-template-columns: 1fr; } 
            .form-container, .preview-container { max-height: none; } 
            
            /* Responsive styling voor moderne sectie */
            .modern-quote-info {
                margin: 15px 0 !important;
                padding: 16px !important;
            }
            .modern-quote-info > div {
                grid-template-columns: 1fr !important;
                gap: 16px !important;
            }
            .modern-info-card {
                padding: 14px !important;
            }
        }

        /* Modern Sender Grid Styles */
        .modern-sender-grid {
          display: flex;
          flex-direction: column;
          gap: 6px;
          font-size: 12.75px;
          width: 100%;
        }

        .sender-company-name {
          font-weight: 700;
          font-size: 13.5px;
          line-height: 1.2;
          color: #2c3e50;
          margin-bottom: 2px;
        }

        .sender-contact-person,
        .sender-address {
          line-height: 1.3;
          color: #34495e;
          margin-bottom: 2px;
        }

        .sender-info-grid {
          display: flex;
          flex-direction: column;
          gap: 6px;
          margin-top: 6px;
        }

        .sender-info-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 4px 0;
          border-bottom: 1px solid rgba(22,160,133,0.05);
        }

        .sender-info-item:last-child {
          border-bottom: none;
        }

        .info-icon {
          width: 16px;
          height: 16px;
          color: var(--primary-color);
          fill: currentColor;
          flex-shrink: 0;
        }

        .info-label {
          color: var(--primary-color);
          font-weight: 600;
          font-size: 11px;
          min-width: 50px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .info-value {
          color: #2c3e50;
          font-weight: 500;
          flex: 1;
        }

        .contact-link {
          color: var(--primary-color);
          text-decoration: none;
          transition: all 0.2s ease;
        }

        .contact-link:hover {
          text-decoration: underline;
          color: #1abc9c;
        }

        /* Compact variations */
        .compact-box .modern-sender-grid {
          font-size: 11.5px;
          gap: 4px;
        }

        .compact-box .sender-company-name {
          font-size: 12px;
        }

        .compact-box .sender-info-grid {
          gap: 4px;
          margin-top: 4px;
        }

        .compact-box .sender-info-item {
          gap: 6px;
          padding: 2px 0;
        }

        .compact-box .info-icon {
          width: 14px;
          height: 14px;
        }

        .compact-box .info-label {
          font-size: 10px;
          min-width: 45px;
        }

        /* Modern Form Styling */
        .modern-form-section {
          background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.9) 100%);
          border: 1px solid rgba(22,160,133,0.1);
          border-radius: 16px;
          padding: 24px;
          margin-bottom: 20px;
          box-shadow: 0 4px 20px rgba(22,160,133,0.08);
          backdrop-filter: blur(10px);
          transition: all 0.3s ease;
        }

        .modern-form-section:hover {
          box-shadow: 0 6px 25px rgba(22,160,133,0.12);
          transform: translateY(-1px);
        }

        .modern-section-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 20px;
          padding-bottom: 12px;
          border-bottom: 2px solid var(--primary-color);
          color: var(--primary-color);
          font-size: 1.1rem;
          font-weight: 700;
        }

        .section-icon {
          width: 22px;
          height: 22px;
          fill: currentColor;
        }

        .modern-form-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 16px;
        }

        .modern-client-form {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .modern-form-group {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .modern-form-group.full-width {
          grid-column: 1 / -1;
        }

        .modern-label {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #2c3e50;
          font-size: 0.9rem;
          margin-bottom: 4px;
        }

        .label-icon {
          width: 16px;
          height: 16px;
          fill: var(--primary-color);
          flex-shrink: 0;
        }

        .modern-input, .modern-textarea {
          padding: 12px 16px;
          border: 2px solid #e1e8ed;
          border-radius: 10px;
          font-size: 0.95rem;
          font-family: 'Open Sans', sans-serif;
          background: rgba(255,255,255,0.9);
          transition: all 0.3s ease;
          outline: none;
        }

        .modern-input:focus, .modern-textarea:focus {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(22,160,133,0.1);
          background: rgba(255,255,255,1);
        }

        .modern-textarea {
          resize: vertical;
          min-height: 80px;
          line-height: 1.5;
        }

        .modern-input::placeholder, .modern-textarea::placeholder {
          color: #95a5a6;
          font-style: italic;
        }

        /* Modern Preview Boxes */
        .modern-parties-info {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 16px;
          margin: 20px 0;
        }

        .modern-info-box {
          background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.9) 100%);
          border: 1px solid rgba(22,160,133,0.15);
          border-radius: 12px;
          padding: 18px;
          box-shadow: 0 4px 15px rgba(22,160,133,0.08);
          backdrop-filter: blur(10px);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .modern-info-box::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background: linear-gradient(90deg, var(--primary-color), #1abc9c);
          border-radius: 12px 12px 0 0;
        }

        .modern-info-box:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(22,160,133,0.12);
        }

        .modern-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;
          color: var(--primary-color);
          font-weight: 700;
          font-size: 0.85rem;
          letter-spacing: 0.5px;
          text-transform: uppercase;
        }

        .header-icon {
          width: 18px;
          height: 18px;
          fill: currentColor;
        }

        .modern-content {
          font-size: 12.75px;
          line-height: 1.4;
          color: #2c3e50;
        }

        /* Recipient specific styles */
        .recipient-company-name {
          font-weight: 700;
          font-size: 13.5px;
          margin-bottom: 4px;
          color: #2c3e50;
        }

        .recipient-contact, .recipient-address {
          margin-bottom: 2px;
          color: #34495e;
        }

        /* Detail box improvements */
        .detail-box {
          background: rgba(246,248,250,0.8);
          border: 1px solid rgba(22,160,133,0.1);
          border-radius: 8px;
          padding: 12px;
        }

        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 4px 0;
          border-bottom: 1px solid rgba(22,160,133,0.05);
        }

        .detail-item:last-child {
          border-bottom: none;
        }

        .detail-label {
          color: var(--primary-color);
          font-weight: 600;
          font-size: 12px;
        }

        .detail-item strong, .detail-item span:last-child {
          font-weight: 600;
          color: #2c3e50;
          text-align: right;
          max-width: 60%;
          word-break: break-word;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .modern-parties-info {
            grid-template-columns: 1fr;
            gap: 12px;
          }

          .modern-info-box {
            padding: 14px;
          }

          .modern-header {
            font-size: 0.8rem;
          }

          .header-icon {
            width: 16px;
            height: 16px;
          }

          .modern-form-grid {
            grid-template-columns: 1fr;
            gap: 12px;
          }

          .modern-form-section {
            padding: 18px;
            margin-bottom: 16px;
          }

          .modern-section-header {
            font-size: 1rem;
            gap: 10px;
          }

          .section-icon {
            width: 20px;
            height: 20px;
          }

          .modern-input, .modern-textarea {
            padding: 10px 14px;
            font-size: 0.9rem;
          }
        }
        
        /* Modern Header & Content Styles */
        .modern-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 10px;
          border-bottom: 2px solid var(--primary-color);
          padding-bottom: 4px;
          width: 100%;
        }
        
        .header-icon {
          width: 18px;
          height: 18px;
          color: var(--primary-color);
        }
        
        .compact-box .header-icon {
          width: 16px;
          height: 16px;
        }
        
        .modern-content {
          width: 100%;
        }
        
        .info-box.compact-box {
          padding: 14px 14px 12px 14px;
        }
        
        .info-box.compact-box h3 {
          margin: 0;
          border-bottom: none;
          padding-bottom: 0;
          font-size: 0.85em;
        }
        
        /* Recipient Styles */
        .recipient-company-name {
          font-weight: 700;
          font-size: 13.2px;
          line-height: 1.2;
          margin-bottom: 4px;
        }
        
        .recipient-contact,
        .recipient-address {
          font-size: 12.75px;
          line-height: 1.4;
          white-space: pre-wrap;
          color: #34495e;
        }
        
        /* Detail Box Styles */
        .detail-box {
          display: flex;
          flex-direction: column;
          gap: 4px;
          width: 100%;
          background: #f6f8fa;
          border-radius: 7px;
          padding: 10px 14px 8px 14px;
          border: 1px solid #e0e4ea;
          box-shadow: 0 1px 4px rgba(22,160,133,0.04);
        }
        
        .detail-item {
          display: grid;
          grid-template-columns: max-content 1fr;
          align-items: center;
          width: 100%;
          padding: 2px 0;
          gap: 8px;
        }
        
        .detail-label {
          color: #16a085;
          font-weight: 600;
          font-size: 12.75px;
        }
        
        .detail-value {
          font-weight: 600;
          word-break: break-all;
          text-align: right;
          font-size: 12.75px;
        }
        
        .project-title {
          word-break: break-word;
        }
        
        /* Modern Quote Header Styles */
        .modern-quote-header {
          display: flex;
          justify-content: space-between;
          align-items: stretch;
          background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
          overflow: hidden;
          border-top-left-radius: 6px;
          border-top-right-radius: 6px;
        }
        
        .modern-logo-area {
          padding: 20px 25px;
          display: flex;
          align-items: center;
          background-color: rgba(255, 255, 255, 0.05);
          border-right: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .modern-logo {
          max-height: 50px;
          max-width: 180px;
          object-fit: contain;
          background: none !important;
          border-radius: 4px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.08);
          filter: brightness(0) invert(1);
          transition: transform 0.3s ease;
        }
        
        .modern-title-area {
          background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
          padding: 0;
          clip-path: polygon(15% 0, 100% 0, 100% 100%, 0% 100%);
          min-width: 250px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
        }
        
        .title-content {
          padding: 20px 30px;
          text-align: right;
          position: relative;
        }
        
        .modern-title-area h1 {
          margin: 0;
          font-family: 'Montserrat', sans-serif;
          font-size: 2.2em;
          letter-spacing: 1px;
          position: relative;
          z-index: 1;
        }
        
        .title-accent {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 60%;
          height: 4px;
          background-color: rgba(255, 255, 255, 0.3);
          border-radius: 2px;
        }
        
        @media print {
          .modern-quote-header {
            background: #2c3e50 !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          
          .modern-title-area {
            background: #16a085 !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          
          .modern-logo {
            max-height: 40px;
          }
          
          .title-content {
            padding: 15px 25px;
          }
          
          .modern-title-area h1 {
            font-size: 1.8em;
          }
          
          .modern-item-table th {
            background-color: #2c3e50 !important;
            color: #fff !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          
          .modern-item-table tr:nth-child(even) {
            background-color: #f9f9f9 !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
        }
        
        /* Modern Item Table Styles */
        .modern-item-table {
          border-collapse: separate;
          border-spacing: 0;
          width: 100%;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0,0,0,0.05);
          margin-bottom: 20px;
        }
        
        /* Modern Totals Section Styles */
        .modern-totals-section {
          display: flex;
          flex-direction: row;
          gap: 30px;
          margin-top: 30px;
        }
        
        .modern-notes {
          flex: 1;
          background: #f8f9fa;
          border-radius: 8px;
          padding: 0;
          box-shadow: 0 2px 8px rgba(0,0,0,0.05);
          overflow: hidden;
        }
        
        .modern-notes-header {
          display: flex;
          align-items: center;
          background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
          color: white;
          padding: 12px 15px;
          border-top-left-radius: 8px;
          border-top-right-radius: 8px;
        }
        
        .notes-icon, .totals-icon {
          width: 24px;
          height: 24px;
          margin-right: 10px;
        }
        
        .modern-notes-header h4, .modern-totals-header h4 {
          margin: 0;
          font-size: 0.95em;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          font-weight: 600;
        }
        
        .modern-notes p {
          padding: 15px;
          margin: 0;
          color: #34495e;
          line-height: 1.5;
        }
        
        .modern-totals-summary {
          width: 300px;
          background: #f8f9fa;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .modern-totals-header {
          display: flex;
          align-items: center;
          background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
          color: white;
          padding: 12px 15px;
          border-top-left-radius: 8px;
          border-top-right-radius: 8px;
        }
        
        .modern-totals-table {
          width: 100%;
          border-collapse: collapse;
        }
        
        .modern-totals-table tr td {
          padding: 12px 15px;
          border-bottom: 1px solid #ecf0f1;
        }
        
        .modern-totals-table tr:last-child td {
          border-bottom: none;
        }
        
        .modern-totals-table tr.grand-total {
          background: #f1f8f6;
          font-weight: 700;
          color: #16a085;
          font-size: 1.1em;
        }
        
        .modern-totals-table tr.grand-total td {
          padding: 15px;
        }
        
        .modern-totals-table td:last-child {
          text-align: right;
          font-weight: 600;
        }
        
        @media print {
          .modern-notes-header, .modern-totals-header, .acceptance-header {
            background: #2c3e50 !important;
            color: #fff !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          
          .modern-totals-table tr.grand-total {
            background: #f1f8f6 !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          
          .modern-acceptance-section {
            page-break-inside: avoid;
          }
        }
        
        /* Modern Acceptance Section Styles */
        .modern-acceptance-section {
          margin-top: 30px;
          background: #f8f9fa;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .acceptance-header {
          display: flex;
          align-items: center;
          background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
          color: white;
          padding: 12px 15px;
          border-top-left-radius: 8px;
          border-top-right-radius: 8px;
        }
        
        .acceptance-icon {
          width: 24px;
          height: 24px;
          margin-right: 10px;
        }
        
        .acceptance-header h3 {
          margin: 0;
          font-size: 0.95em;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          font-weight: 600;
        }
        
        .acceptance-content {
          padding: 20px;
        }
        
        .acceptance-content p {
          margin: 0 0 20px 0;
          color: #34495e;
          line-height: 1.5;
        }
        
        .modern-signature-area {
          display: flex;
          justify-content: space-between;
          gap: 20px;
          margin-top: 20px;
        }
        
        .modern-signature-block {
          flex: 1;
          display: flex;
          flex-direction: column;
        }
        
        .signature-label {
          font-size: 0.85em;
          color: #7f8c8d;
          margin-bottom: 8px;
        }
        
        .signature-line {
          height: 1px;
          background-color: #bdc3c7;
          margin-top: 40px;
        }
        
        /* Modern Footer Styles */
        .modern-footer {
          margin-top: 40px;
          padding: 20px 0;
          border-top: 1px solid #ecf0f1;
        }
        
        .footer-content {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 15px;
        }
        
        .footer-company strong {
          color: #2c3e50;
          font-weight: 600;
          font-size: 0.95em;
        }
        
        .footer-divider {
          width: 4px;
          height: 4px;
          border-radius: 50%;
          background-color: #bdc3c7;
        }
        
        .footer-website {
          color: #16a085;
          font-size: 0.95em;
        }
        
        .modern-item-table thead {
          background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
          color: #fff;
        }
        
        .modern-item-table th {
          padding: 12px 15px;
          text-align: left;
          font-weight: 700;
          font-size: 0.85em;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          border-bottom: 2px solid rgba(255,255,255,0.1);
        }
        
        .modern-item-table th:first-child {
          border-top-left-radius: 8px;
        }
        
        .modern-item-table th:last-child {
          border-top-right-radius: 8px;
        }
        
        .modern-table-body tr {
          border-bottom: 1px solid #ecf0f1;
          transition: background-color 0.2s ease;
        }
        
        .modern-table-body tr:nth-child(even) {
          background-color: #f9f9f9;
        }
        
        .modern-table-body tr:last-child {
          border-bottom: none;
        }
        
        .modern-item-table td {
          padding: 12px 15px;
          vertical-align: top;
        }
        
        .modern-item-table td:first-child strong {
          color: #2c3e50;
          font-weight: 600;
          font-size: 1.05em;
        }
        
        .modern-item-table .description {
          font-size: 0.85em;
          color: #7f8c8d;
          margin: 5px 0 0 0;
          line-height: 1.4;
        }
        
        .modern-item-table th:nth-child(n+2),
        .modern-item-table td:nth-child(n+2) {
          text-align: right;
        }

        .voorwaarden-breed {
            width: 100%;
            grid-column: 1 / -1;
            margin: 24px 0 0 0;
        }
        .voorwaarden-breed h3 {
            font-family: 'Montserrat', sans-serif;
            color: #2c3e50;
            margin: 0 0 8px 0;
            font-size: 1.1em;
        }
        .voorwaarden-breed p {
            width: 100%;
            box-sizing: border-box;
            background: #f9f9f9;
            border-radius: 8px;
            padding: 18px 28px;
            font-size: 1em;
            color: #34495e;
            margin: 0 0 24px 0;
        }
        .voorwaarden-breed.verbeterd {
            width: 100%;
            grid-column: 1 / -1;
            margin: 28px 0 0 0;
            background: linear-gradient(90deg, #e0f7fa 0%, #f9f9f9 100%);
            border: 2px solid #16a085;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(22,160,133,0.08);
            padding: 0 0 0 0;
        }
        .voorwaarden-breed.verbeterd h3 {
            font-family: 'Montserrat', sans-serif;
            color: #16a085;
            margin: 0 0 8px 0;
            font-size: 1.15em;
            padding: 18px 28px 0 28px;
        }
        .voorwaarden-breed.verbeterd p {
            width: 100%;
            box-sizing: border-box;
            background: transparent;
            border-radius: 0 0 10px 10px;
            padding: 10px 28px 22px 28px;
            font-size: 1.05em;
            color: #2c3e50;
            margin: 0 0 0 0;
            line-height: 1.7;
        }
        .voorwaarden-breed.verbeterd a {
            color: #16a085;
            text-decoration: underline;
        }
        .voorwaarden-breed.verbeterd strong {
            color: #e67e22;
        }
        .voorwaarden-breed.mooi {
            width: 100%;
            grid-column: 1 / -1;
            margin: 28px 0 0 0;
            background: #ecf0f1;
            border: 1.5px solid #16a085;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(44,62,80,0.06);
            padding: 0;
        }
        .voorwaarden-breed.mooi h3 {
            font-family: 'Montserrat', sans-serif;
            color: #16a085;
            margin: 0 0 8px 0;
            font-size: 1.1em;
            padding: 18px 28px 0 28px;
            letter-spacing: 1px;
            text-transform: uppercase;
        }
        .voorwaarden-breed.mooi p {
            width: 100%;
            box-sizing: border-box;
            background: transparent;
            border-radius: 0 0 8px 8px;
            padding: 10px 28px 22px 28px;
            font-size: 15px;
            color: #34495e;
            margin: 0 0 0 0;
            line-height: 1.6;
            font-family: 'Open Sans', sans-serif;
        }
        .voorwaarden-breed.mooi.compact {
            max-width: 700px;
            margin: 28px auto 0 auto;
            background: #ecf0f1;
            border: 1.5px solid #16a085;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(44,62,80,0.06);
            padding: 0;
        }
        .voorwaarden-breed.mooi.compact h3 {
            font-family: 'Montserrat', sans-serif;
            color: #16a085;
            margin: 0 0 8px 0;
            font-size: 1.1rem;
            padding: 16px 22px 0 22px;
            letter-spacing: 1px;
            text-transform: uppercase;
        }
        .voorwaarden-breed.mooi.compact p {
            width: 100%;
            box-sizing: border-box;
            background: transparent;
            border-radius: 0 0 8px 8px;
            padding: 8px 22px 18px 22px;
            font-size: 15px;
            color: #34495e;
            margin: 0 0 0 0;
            line-height: 1.6;
            font-family: 'Open Sans', sans-serif;
        }
        .voorwaarden-breed.mooi.compact.klein {
            max-width: 480px;
            margin: 24px auto 0 auto;
            background: #ecf0f1;
            border: 1.5px solid #16a085;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(44,62,80,0.06);
            padding: 0;
        }
        .voorwaarden-breed.mooi.compact.klein h3 {
            font-family: 'Montserrat', sans-serif;
            color: #16a085;
            margin: 0 0 8px 0;
            font-size: 0.9rem;
            font-weight: 700;
            padding: 12px 16px 0 16px;
            letter-spacing: 1px;
            text-transform: uppercase;
        }
        .voorwaarden-breed.mooi.compact.klein p {
            width: 100%;
            box-sizing: border-box;
            background: transparent;
            border-radius: 0 0 8px 8px;
            padding: 6px 16px 12px 16px;
            font-size: 15px;
            color: #34495e;
            margin: 0 0 0 0;
            line-height: 1.6;
            font-family: 'Open Sans', sans-serif;
            font-weight: 400;
        }
        .voorwaarden-breed.full-width {
            width: 100%;
            margin: 28px 0 0 0;
            background: #ecf0f1;
            border: 1.5px solid #16a085;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(44,62,80,0.06);
            padding: 0;
            grid-column: 1 / -1;
        }
        .voorwaarden-breed.full-width h4 {
            font-family: 'Montserrat', sans-serif;
            color: #2c3e50;
            margin: 0 0 8px 0;
            font-size: 0.9rem;
            font-weight: 700;
            padding: 18px 28px 0 28px;
            letter-spacing: 1px;
            text-transform: none;
        }
        .voorwaarden-breed.full-width p {
            width: 100%;
            box-sizing: border-box;
            background: transparent;
            border-radius: 0 0 8px 8px;
            padding: 8px 28px 18px 28px;
            font-size: 15px;
            color: #34495e;
            margin: 0 0 0 0;
            line-height: 1.6;
            font-family: 'Open Sans', sans-serif;
            font-weight: 400;
        }
        .voorwaarden-breed.full-width.small {
            width: 90%;
            margin: 28px auto 0 auto;
            background: #ecf0f1;
            border: 1.5px solid #16a085;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(44,62,80,0.06);
            padding: 0;
            grid-column: 1 / -1;
        }
        .voorwaarden-breed.full-width.small h4 {
            font-family: 'Montserrat', sans-serif;
            color: #2c3e50;
            margin: 0 0 8px 0;
            font-size: 0.82rem;
            font-weight: 700;
            padding: 14px 20px 0 20px;
            letter-spacing: 1px;
            text-transform: none;
        }
        .voorwaarden-breed.full-width.small p {
            width: 100%;
            box-sizing: border-box;
            background: transparent;
            border-radius: 0 0 8px 8px;
            padding: 6px 20px 14px 20px;
            font-size: 13px;
            color: #34495e;
            margin: 0 0 0 0;
            line-height: 1.5;
            font-family: 'Open Sans', sans-serif;
            font-weight: 400;
        }
        .voorwaarden-breed.full-width.custom {
            width: 96%;
            margin: 28px auto 0 auto;
            background: #f6f8fa;
            border: 1.5px solid #3498db;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(52,152,219,0.08);
            padding: 0;
            grid-column: 1 / -1;
        }
        .voorwaarden-breed.full-width.custom h4 {
            font-family: 'Montserrat', sans-serif;
            color: #3498db;
            margin: 0 0 8px 0;
            font-size: 0.9rem;
            font-weight: 700;
            padding: 14px 22px 0 22px;
            letter-spacing: 1px;
            text-transform: none;
        }
        .voorwaarden-breed.full-width.custom p {
            width: 100%;
            box-sizing: border-box;
            background: transparent;
            border-radius: 0 0 8px 8px;
            padding: 8px 22px 16px 22px;
            font-size: 0.95rem;
            color: #34495e;
            margin: 0 0 0 0;
            line-height: 1.6;
            font-family: 'Open Sans', sans-serif;
            font-weight: 400;
        }
        .voorwaarden-breed.full-width.custom.template-color {
            width: 96%;
            margin: 28px auto 0 auto;
            background: #ecf0f1;
            border: 1.5px solid #16a085;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(22,160,133,0.08);
            padding: 0;
            grid-column: 1 / -1;
        }
        .voorwaarden-breed.full-width.custom.template-color h4 {
            font-family: 'Montserrat', sans-serif;
            color: #16a085;
            margin: 0 0 8px 0;
            font-size: 0.9rem;
            font-weight: 700;
            padding: 14px 22px 0 22px;
            letter-spacing: 1px;
            text-transform: none;
        }
        .voorwaarden-breed.full-width.custom.template-color p {
            width: 100%;
            box-sizing: border-box;
            background: transparent;
            border-radius: 0 0 8px 8px;
            padding: 8px 22px 16px 22px;
            font-size: 12px;
            color: #34495e;
            margin: 0 0 0 0;
            line-height: 1.6;
            font-family: 'Open Sans', sans-serif;
            font-weight: 400;
        }

        .modern-voorwaarden {
          padding: 0 0 18px 0 !important;
          background: #f8fafb !important;
          border: 1.5px solid #16a085 !important;
          border-radius: 10px !important;
          box-shadow: 0 2px 8px rgba(22,160,133,0.06) !important;
          margin-top: 28px !important;
          margin-bottom: 0 !important;
          width: 98% !important;
          max-width: 820px;
        }
        .modern-voorwaarden h4 {
          font-family: 'Montserrat', sans-serif;
          color: #16a085;
          font-size: 1.08rem;
          font-weight: 700;
          padding: 18px 28px 0 28px;
          margin: 0 0 8px 0;
          letter-spacing: 1px;
          text-transform: uppercase;
        }
        .voorwaarden-divider {
          border: none;
          border-top: 1.5px solid #e0e4ea;
          margin: 0 28px 12px 28px;
        }
        .voorwaarden-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 0 32px;
          padding: 0 28px;
        }
        .voorwaarden-col {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }
        .voorwaarden-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #16a085;
          margin-top: 10px;
          font-size: 1em;
        }
        .voorwaarden-icon {
          font-size: 1.1em;
          margin-right: 2px;
        }
        .voorwaarden-title {
          font-family: 'Montserrat', sans-serif;
          font-size: 1em;
          color: #16a085;
        }
        .voorwaarden-text {
          font-size: 0.97em;
          color: #34495e;
          margin-left: 28px;
          margin-bottom: 2px;
          line-height: 1.5;
        }
        @media (max-width: 700px) {
          .voorwaarden-grid {
            grid-template-columns: 1fr;
            gap: 0;
            padding: 0 10px;
          }
          .modern-voorwaarden {
            width: 100% !important;
            max-width: 100vw;
            padding: 0 0 10px 0 !important;
          }
          .voorwaarden-divider {
            margin: 0 10px 10px 10px;
          }
        }
        .compact-voorwaarden {
          padding: 0 0 6px 0 !important;
          background: #f8fafb !important;
          border: 1px solid #16a085 !important;
          border-radius: 7px !important;
          box-shadow: 0 1px 4px rgba(22,160,133,0.04) !important;
          margin-top: 16px !important;
          margin-bottom: 0 !important;
          width: 99% !important;
          max-width: 600px;
        }
        .compact-voorwaarden h4 {
          font-family: 'Montserrat', sans-serif;
          color: #16a085;
          font-size: 0.98rem;
          font-weight: 700;
          padding: 10px 14px 0 14px;
          margin: 0 0 4px 0;
          letter-spacing: 0.5px;
          text-transform: uppercase;
        }
        .voorwaarden-divider {
          border: none;
          border-top: 1px solid #e0e4ea;
          margin: 0 14px 6px 14px;
        }
        .voorwaarden-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 0 12px;
          padding: 0 14px;
        }
        .voorwaarden-col {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }
        .voorwaarden-item {
          display: flex;
          align-items: center;
          gap: 5px;
          font-weight: 600;
          color: #16a085;
          margin-top: 6px;
          font-size: 0.93em;
        }
        .voorwaarden-icon {
          font-size: 0.95em;
          margin-right: 1px;
        }
        .voorwaarden-title {
          font-family: 'Montserrat', sans-serif;
          font-size: 0.93em;
          color: #16a085;
        }
        .voorwaarden-text {
          font-size: 0.89em;
          color: #34495e;
          margin-left: 18px;
          margin-bottom: 1px;
          line-height: 1.35;
        }

        .ultra-compact-voorwaarden {
          padding: 0 0 2px 0 !important;
          background: #f8fafb !important;
          border: 1px solid #16a085 !important;
          border-radius: 6px !important;
          box-shadow: 0 1px 2px rgba(22,160,133,0.03) !important;
          margin-top: 10px !important;
          margin-bottom: 0 !important;
          width: 99% !important;
          max-width: 480px;
        }
        .ultra-compact-voorwaarden h4 {
          font-family: 'Montserrat', sans-serif;
          color: #16a085;
          font-size: 0.89rem;
          font-weight: 700;
          padding: 6px 8px 0 8px;
          margin: 0 0 2px 0;
          letter-spacing: 0.2px;
          text-transform: uppercase;
        }
        .voorwaarden-divider {
          border: none;
          border-top: 1px solid #e0e4ea;
          margin: 0 8px 2px 8px;
        }
        .voorwaarden-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 0 4px;
          padding: 0 8px;
        }
        .voorwaarden-col {
          display: flex;
          flex-direction: column;
          gap: 2px;
        }
        .voorwaarden-item {
          display: flex;
          align-items: center;
          gap: 3px;
          font-weight: 600;
          color: #16a085;
          margin-top: 2px;
          font-size: 0.82em;
        }
        .voorwaarden-icon {
          font-size: 0.82em;
          margin-right: 0px;
        }
        .voorwaarden-title {
          font-family: 'Montserrat', sans-serif;
          font-size: 0.82em;
          color: #16a085;
        }
        .voorwaarden-text {
          font-size: 0.78em;
          color: #34495e;
          margin-left: 12px;
          margin-bottom: 0;
          line-height: 1.2;
        }

        /* Nieuwe stijl voor de brede voorwaarden sectie */
        .voorwaarden-breed.full-width.custom.template-color.wide-voorwaarden {
          padding: 0 0 8px 0 !important;
          background: #f8fafb !important;
          border: 1px solid #16a085 !important;
          border-radius: 8px !important;
          box-shadow: 0 1px 4px rgba(22,160,133,0.04) !important;
          margin-top: 10px !important;
          margin-bottom: 0 !important;
          width: 99.5% !important;
          max-width: 700px;
        }
        .voorwaarden-breed.full-width.custom.template-color.wide-voorwaarden h4 {
          font-family: 'Montserrat', sans-serif;
          color: #16a085;
          font-size: 1.02rem;
          font-weight: 700;
          padding: 10px 18px 0 18px;
          margin: 0 0 4px 0;
          letter-spacing: 0.5px;
          text-transform: uppercase;
        }
        .voorwaarden-breed.full-width.custom.template-color.wide-voorwaarden .voorwaarden-divider {
          border: none;
          border-top: 1px solid #e0e4ea;
          margin: 0 18px 6px 18px;
        }
        .voorwaarden-breed.full-width.custom.template-color.wide-voorwaarden .wide-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 0 32px;
          padding: 0 18px;
        }
        .voorwaarden-breed.full-width.custom.template-color.wide-voorwaarden .voorwaarden-col {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }
        .voorwaarden-breed.full-width.custom.template-color.wide-voorwaarden .voorwaarden-item {
          display: flex;
          align-items: center;
          gap: 5px;
          font-weight: 600;
          color: #16a085;
          margin-top: 6px;
          font-size: 0.93em;
        }
        .voorwaarden-breed.full-width.custom.template-color.wide-voorwaarden .voorwaarden-icon {
          font-size: 0.93em;
          margin-right: 1px;
        }
        .voorwaarden-breed.full-width.custom.template-color.wide-voorwaarden .voorwaarden-title {
          font-family: 'Montserrat', sans-serif;
          font-size: 0.93em;
          color: #16a085;
        }
        .voorwaarden-breed.full-width.custom.template-color.wide-voorwaarden .voorwaarden-text {
          font-size: 0.89em;
          color: #34495e;
          margin-left: 18px;
          margin-bottom: 1px;
          line-height: 1.35;
        }
        @media (max-width: 900px) {
          .voorwaarden-breed.full-width.custom.template-color.wide-voorwaarden {
            max-width: 99vw;
            width: 100% !important;
            padding: 0 0 4px 0 !important;
          }
          .voorwaarden-breed.full-width.custom.template-color.wide-voorwaarden .wide-grid {
            grid-template-columns: 1fr;
            gap: 0;
            padding: 0 8px;
          }
          .voorwaarden-breed.full-width.custom.template-color.wide-voorwaarden .voorwaarden-divider {
            margin: 0 8px 6px 8px;
          }
        }

        .voorwaarden-breed.voorwaarden-3col {
          width: 100vw;
          max-width: 100vw;
          margin-left: calc(-1 * (50vw - 50%));
          margin-right: calc(-1 * (50vw - 50%));
          background: #f8fafb;
          border: 1px solid #16a085;
          border-radius: 8px;
          box-shadow: 0 1px 4px rgba(22,160,133,0.04);
          padding: 0 0 8px 0;
          margin-top: 10px;
          margin-bottom: 0;
          box-sizing: border-box;
          overflow-x: auto;
        }
        .voorwaarden-3col-grid {
          display: grid;
          grid-template-columns: repeat(3, minmax(220px, 1fr));
          gap: 0 32px;
          padding: 0 24px;
          min-width: 660px;
          width: 100%;
        }
        .voorwaarden-3col-col {
          display: flex;
          flex-direction: column;
          gap: 4px;
          min-width: 220px;
        }
        .voorwaarden-item {
          display: flex;
          align-items: center;
          gap: 5px;
          font-weight: 600;
          color: #16a085;
          margin-top: 6px;
          font-size: 0.93em;
        }
        .voorwaarden-icon {
          font-size: 0.93em;
          margin-right: 1px;
        }
        .voorwaarden-text {
          font-size: 0.89em;
          color: #34495e;
          margin-left: 18px;
          margin-bottom: 1px;
          line-height: 1.35;
        }
        @media (max-width: 900px) {
          .voorwaarden-3col-grid {
            grid-template-columns: 1fr 1fr;
            min-width: 440px;
          }
        }
        @media (max-width: 600px) {
          .voorwaarden-breed.voorwaarden-3col {
            width: 100vw;
            max-width: 100vw;
            margin-left: -8px;
            margin-right: -8px;
            padding: 0 0 4px 0;
          }
          .voorwaarden-3col-grid {
            grid-template-columns: 1fr;
            min-width: 0;
            gap: 0;
            padding: 0 4px;
          }
        }
        @media print {
          .voorwaarden-breed.voorwaarden-3col {
            width: calc(210mm - 5mm) !important;
            max-width: calc(210mm - 5mm) !important;
            margin-left: 2.5mm !important;
            margin-right: 2.5mm !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
          }
          .voorwaarden-3col-grid {
            grid-template-columns: repeat(3, 1fr) !important;
            min-width: 0 !important;
          }
        }
        .verbeterde-voorwaarden {
          width: 100%;
          max-width: 100%;
          margin: 10px 0 0 0;
          background: #f8fafb;
          border: 1px solid #16a085;
          border-radius: 7px;
          box-shadow: 0 1px 4px rgba(22,160,133,0.04);
          padding: 12px 16px 8px 16px;
          box-sizing: border-box;
          overflow-x: visible;
        }
        .verbeterde-voorwaarden h4 {
          font-family: 'Montserrat', sans-serif;
          color: #16a085;
          font-size: 1.05rem;
          font-weight: 700;
          padding: 10px 18px 0 18px;
          margin: 0 0 4px 0;
          letter-spacing: 0.5px;
          text-transform: uppercase;
        }
        .verbeterde-voorwaarden-grid {
          display: flex;
          flex-wrap: wrap;
          gap: 0 12px;
          padding: 0;
          width: 100%;
        }
        .voorwaarden-3col-col {
          display: flex;
          flex-direction: column;
          gap: 1px;
          min-width: 220px;
          flex: 1 1 0;
        }
        .voorwaarden-item {
          display: flex;
          align-items: center;
          gap: 3px;
          font-weight: 600;
          color: #16a085;
          margin-top: 1px;
          font-size: 0.78em;
          white-space: normal;
        }
        .voorwaarden-icon {
          font-size: 0.85em;
          margin-right: 1px;
        }
        .voorwaarden-text {
          font-size: 0.72em;
          color: #34495e;
          margin-left: 10px;
          margin-bottom: 0;
          line-height: 1.13;
          white-space: normal;
          word-break: break-word;
        }
        .voorwaarden-divider {
          border: none;
          border-top: 1px solid #e0e4ea;
          margin: 0 2px 2px 2px;
        }
        @media (max-width: 1100px) {
          .verbeterde-voorwaarden-grid {
            flex-wrap: wrap;
            gap: 0 8px;
          }
        }
        @media (max-width: 700px) {
          .verbeterde-voorwaarden {
            width: 100%;
            max-width: 100%;
            margin-left: 0;
            margin-right: 0;
            padding: 8px 4px 4px 4px;
          }
          .verbeterde-voorwaarden-grid {
            flex-direction: column;
            gap: 0;
          }
          .voorwaarden-divider {
            margin: 0 2px 2px 2px;
          }
        }
        @media print {
          .verbeterde-voorwaarden {
            width: 100% !important;
            max-width: 100% !important;
            margin-left: 0 !important;
            margin-right: 0 !important;
            padding-left: 16px !important;
            padding-right: 16px !important;
          }
          .verbeterde-voorwaarden-grid {
            flex-wrap: wrap !important;
            gap: 0 8px !important;
          }
          .voorwaarden-item {
            font-size: 0.70em !important;
            margin-top: 0 !important;
          }
          .voorwaarden-text {
            font-size: 0.65em !important;
            margin-left: 6px !important;
            line-height: 1.05 !important;
          }
          .voorwaarden-divider {
            margin: 0 2px 1px 2px !important;
          }
        }
    </style>


    <!-- Print optimalisatie: maximale breedte, marges 2,5mm -->
    <style>
    @page {
      size: A4;
      margin: 2.5mm;
    }
    @media print {
      html, body {
        width: 210mm;
        height: 297mm;
        margin: 0;
        padding: 0;
        background: #fff !important;
      }
      body {
        zoom: 1;
      }
      .quote-container {
        width: 205mm !important;
        max-width: 205mm !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        font-size: 12px !important;
        page-break-inside: avoid !important;
      }
      .quote-main, .totals-section, .meta-info, .parties-info, .acceptance-section {
        padding: 6px 10px !important;
      }
      .item-table th, .item-table td {
        padding: 3px 5px !important;
        font-size: 11px !important;
      }
      .item-table {
        font-size: 11px !important;
      }
      .title-area, .logo-area {
        padding: 6px 10px !important;
      }
      .notes {
        width: 100% !important;
      }
      .voorwaarden-breed.full-width.custom.template-color {
        margin: 10px 0 0 0 !important;
        padding: 0 5px !important;
      }
      .voorwaarden-breed.full-width.custom.template-color p {
        padding: 4px 8px 8px 8px !important;
        font-size: 10px !important;
      }
      .signature-area {
        gap: 8px !important;
      }
      .quote-footer {
        padding: 5px 10px !important;
        font-size: 9px !important;
      }
      /* Voorkom page-breaks binnen belangrijke secties */
      .quote-container, .quote-main, .item-table, .totals-section, .meta-info, .parties-info, .acceptance-section, .voorwaarden-breed {
        page-break-inside: avoid !important;
        break-inside: avoid !important;
      }
      /* Verberg niet-relevante elementen voor print */
      .no-print, .hide-print {
        display: none !important;
      }
    }
    </style>
</head>
<body style="font-size: 12.75px;">

    <!-- Applicatie Header -->
    <header class="app-header">
        <h1>Offerte Generator</h1>
        <div class="actions">
            <button id="settings-btn">Instellingen</button>
            <button id="generate-pdf">Genereer PDF</button>
        </div>
    </header>

    <!-- Hoofdcontainer met Formulier en Preview -->
    <main class="main-container">
        
        <!-- Linkerkolom: Formulier -->
        <div class="form-container">
            <form id="quote-form" onsubmit="return false;">
                
                <div class="form-section modern-form-section">
                    <h2 class="modern-section-header">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="section-icon">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20M12,19L8,15H10.5V12H13.5V15H16L12,19Z" />
                        </svg>
                        <span>Offerte Details</span>
                    </h2>
                    <div class="modern-form-grid">
                        <div class="modern-form-group">
                            <label for="offertenummer" class="modern-label">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="label-icon">
                                    <path d="M9,3V4H4V6H5V19A2,2 0 0,0 7,21H17A2,2 0 0,0 19,19V6H20V4H15V3H9M7,6H17V19H7V6M9,8V17H11V8H9M13,8V17H15V8H13Z"/>
                                </svg>
                                Offertenummer
                            </label>
                            <input type="text" id="offertenummer" class="modern-input" data-target="#preview-offertenummer" value="OFF-2025-001">
                        </div>
                        <div class="modern-form-group">
                            <label for="datum" class="modern-label">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="label-icon">
                                    <path d="M19,3H18V1H16V3H8V1H6V3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V8H19V19Z"/>
                                </svg>
                                Datum
                            </label>
                            <input type="date" id="datum" class="modern-input" data-target="#preview-datum">
                        </div>
                        <div class="modern-form-group">
                            <label for="geldig-tot" class="modern-label">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="label-icon">
                                    <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
                                </svg>
                                Geldig tot
                            </label>
                            <input type="date" id="geldig-tot" class="modern-input" data-target="#preview-geldig-tot">
                        </div>
                        <div class="modern-form-group">
                            <label for="project-titel" class="modern-label">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="label-icon">
                                    <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
                                </svg>
                                Project
                            </label>
                            <input type="text" id="project-titel" class="modern-input" data-target="#preview-project-titel" value="Nieuwe Website & Branding">
                        </div>
                    </div>
                </div>

                <div class="form-section modern-form-section">
                    <h2 class="modern-section-header">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="section-icon">
                            <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z" />
                        </svg>
                        <span>Klantgegevens</span>
                    </h2>
                    <div class="modern-client-form">
                        <div class="modern-form-group full-width">
                            <label for="ontvanger-naam" class="modern-label">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="label-icon">
                                    <path d="M12,7A8,8 0 0,1 20,15A8,8 0 0,1 12,23A8,8 0 0,1 4,15A8,8 0 0,1 12,7M12,9A6,6 0 0,0 6,15A6,6 0 0,0 12,21A6,6 0 0,0 18,15A6,6 0 0,0 12,9M12,2L14,5H10L12,2M3.5,6L6,8.5L4.5,10L2,7.5L3.5,6M20.5,6L22,7.5L19.5,10L18,8.5L20.5,6Z"/>
                                </svg>
                                Bedrijfsnaam
                            </label>
                            <input type="text" id="ontvanger-naam" class="modern-input" data-target="#preview-ontvanger-naam" value="[ONTVANGER]" placeholder="Naam van het bedrijf">
                        </div>
                        <div class="modern-form-group full-width">
                            <label for="ontvanger-contact" class="modern-label">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="label-icon">
                                    <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
                                </svg>
                                Contactpersoon
                            </label>
                            <input type="text" id="ontvanger-contact" class="modern-input" data-target="#preview-ontvanger-contact" value="[Contactpersoon]" placeholder="Voor- en achternaam">
                        </div>
                        <div class="modern-form-group full-width">
                            <label for="ontvanger-adres" class="modern-label">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="label-icon">
                                    <path d="M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5M12,2A7,7 0 0,0 5,9C5,14.25 12,22 12,22S19,14.25 19,9A7,7 0 0,0 12,2Z"/>
                                </svg>
                                Adres
                            </label>
                            <textarea id="ontvanger-adres" class="modern-textarea" data-target="#preview-ontvanger-adres" rows="3" placeholder="Straatnaam en huisnummer&#10;Postcode en plaats">[Adres van de klant]
[Postcode en Stad]</textarea>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h2><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13.5,8H12V13.5H13.5V8M13.5,15.5H12V17H13.5V15.5M21,3H3A2,2 0 0,0 1,5V19A2,2 0 0,0 3,21H21A2,2 0 0,0 23,19V5A2,2 0 0,0 21,3M21,19H3V5H21V19Z" /></svg> Kostenoverzicht</h2>
                    <div id="items-form-container"></div>
                    <button type="button" id="add-item-btn">+ Regel Toevoegen</button>
                </div>
                
                <div class="form-section">
                    <h2><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.13,5.12L18.88,8.87M3,17.25V21H6.75L17.81,9.94L14.06,6.19L3,17.25Z" /></svg> Aanvullende Informatie</h2>
                    <div class="form-group full-width"><label for="opmerkingen">Opmerkingen</label><textarea id="opmerkingen" data-target="#preview-opmerkingen">Alle genoemde prijzen zijn exclusief 21% BTW. Meerwerk wordt in overleg gefactureerd.</textarea></div>
                    <div class="form-group full-width"><label for="voorwaarden">Voorwaarden</label><textarea id="voorwaarden" rows="6" placeholder="Vul hier de offertevoorwaarden in..."></textarea></div>
                <div class="form-group full-width" style="text-align:right; margin-top:8px;">
                  <button type="button" id="save-aanvullende-btn" style="background: var(--primary-color); color: white; border: none; padding: 8px 18px; border-radius: 5px; cursor: pointer; font-weight: 600; font-size: 0.95rem;">Opslaan</button>
<!-- Modern floating alert -->
<div id="save-alert" style="display:none; position:fixed; top:38px; left:50%; transform:translateX(-50%); z-index:3000; min-width:320px; max-width:96vw; box-sizing:border-box; pointer-events:none;"></div>
                </div>
              </div>
            </form>
        </div>

        <!-- Rechterkolom: Offerte Preview -->
        <div class="preview-container">
            <div id="quote-preview">
                <div class="quote-container">
                    <header class="quote-header modern-quote-header">
                        <div class="logo-area modern-logo-area">
                            <img src="https://via.placeholder.com/150x50.png?text=Bedrijfslogo" alt="Bedrijfslogo" id="preview-logo" class="modern-logo" />
                        </div>
                        <div class="title-area modern-title-area">
                            <div class="title-content">
                                <h1>OFFERTE</h1>
                                <div class="title-accent"></div>
                            </div>
                        </div>
                    </header>
                    <section class="meta-info">
                    </section>
                    <section class="parties-info modern-parties-info">
                        <!-- Ontvanger Box -->
                        <div class="info-box modern-info-box recipient-box">
                          <div class="modern-header">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="header-icon">
                              <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
                            </svg>
                            <span>ONTVANGER</span>
                          </div>
                          <div class="modern-content">
                            <div class="recipient-company-name" id="preview-ontvanger-naam"></div>
                            <div class="recipient-contact">T.a.v. <span id="preview-ontvanger-contact"></span></div>
                            <div class="recipient-address" id="preview-ontvanger-adres"></div>
                          </div>
                        </div>

                        <!-- Verzender Box -->
                        <div class="info-box modern-info-box sender-box">
                          <div class="modern-header">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="header-icon">
                              <path d="M12,7A8,8 0 0,1 20,15A8,8 0 0,1 12,23A8,8 0 0,1 4,15A8,8 0 0,1 12,7M12,9A6,6 0 0,0 6,15A6,6 0 0,0 12,21A6,6 0 0,0 18,15A6,6 0 0,0 12,9M12,2L14,5H10L12,2M3.5,6L6,8.5L4.5,10L2,7.5L3.5,6M20.5,6L22,7.5L19.5,10L18,8.5L20.5,6Z"/>
                            </svg>
                            <span>VERZENDER</span>
                          </div>
                          <div class="modern-content" id="preview-verzender-info"></div>
                        </div>

                        <!-- Offerte Details Box -->
                        <div class="info-box modern-info-box details-box">
                          <div class="modern-header">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="header-icon">
                              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20M12,19L8,15H10.5V12H13.5V15H16L12,19Z"/>
                            </svg>
                            <span>OFFERTE DETAILS</span>
                          </div>
                          <div class="modern-content">
                            <div class="detail-box">
                              <div class="detail-item">
                                <span class="detail-label">Offertenummer:</span>
                                <strong id="preview-offertenummer">OFF-2025-001</strong>
                              </div>
                              <div class="detail-item">
                                <span class="detail-label">Datum:</span>
                                <strong id="preview-datum">09-07-2025</strong>
                              </div>
                              <div class="detail-item">
                                <span class="detail-label">Geldig tot:</span>
                                <strong id="preview-geldig-tot">08-08-2025</strong>
                              </div>
                              <div class="detail-item">
                                <span class="detail-label">Project:</span>
                                <span id="preview-project-titel">Nieuwe Website &amp; Branding</span>
                              </div>
                            </div>
                          </div>
                        </div>
                    </section>
                    <main class="quote-main">
                        <h3>Kostenoverzicht: <span id="preview-project-titel"></span></h3>
                        <table class="item-table modern-item-table">
                            <thead>
                                <tr>
                                    <th>Omschrijving</th>
                                    <th>Aantal</th>
                                    <th>Prijs p/s</th>
                                    <th>BTW %</th>
                                    <th>Totaal</th>
                                </tr>
                            </thead>
                            <tbody id="preview-items-body" class="modern-table-body">
<tr><td><strong>Strategie & Conceptontwikkeling</strong><p class="description">Uitwerken van visuele identiteit en projectstrategie.</p></td><td>1</td><td>€&nbsp;850,00</td><td>21%</td><td>€&nbsp;850,00</td></tr>
<tr><td><strong>Webdesign & Realisatie</strong><p class="description">Ontwerp en bouw van een maatwerk WordPress website.</p></td><td>1</td><td>€&nbsp;3.200,00</td><td>21%</td><td>€&nbsp;3.200,00</td></tr>
<tr><td><strong>Service & Hosting (1 jaar)</strong><p class="description">Hosting, onderhoud, updates en support.</p></td><td>1</td><td>€&nbsp;450,00</td><td>21%</td><td>€&nbsp;450,00</td></tr>
<tr><td><strong>SEO Optimalisatie</strong><p class="description">Basis zoekmachineoptimalisatie voor betere vindbaarheid.</p></td><td>1</td><td>€&nbsp;300,00</td><td>21%</td><td>€&nbsp;300,00</td></tr>
<tr><td><strong>Content Integratie</strong><p class="description">Plaatsen en structureren van aangeleverde teksten en afbeeldingen.</p></td><td>1</td><td>€&nbsp;200,00</td><td>21%</td><td>€&nbsp;200,00</td></tr>
<tr><td><strong>Training & Instructie</strong><p class="description">Uitleg en training voor het gebruik van het CMS.</p></td><td>1</td><td>€&nbsp;150,00</td><td>21%</td><td>€&nbsp;150,00</td></tr>
<tr><td><strong>Supportpakket (6 maanden)</strong><p class="description">Telefonische en e-mail support na oplevering.</p></td><td>1</td><td>€&nbsp;180,00</td><td>21%</td><td>€&nbsp;180,00</td></tr>
<tr><td><strong>SSL Certificaat</strong><p class="description">Beveiligde verbinding voor de website.</p></td><td>1</td><td>€&nbsp;75,00</td><td>21%</td><td>€&nbsp;75,00</td></tr>
<tr><td><strong>Back-up Service (1 jaar)</strong><p class="description">Automatische back-ups van de website.</p></td><td>1</td><td>€&nbsp;120,00</td><td>21%</td><td>€&nbsp;120,00</td></tr>
<tr style="border-bottom:1px solid #e0e4ea;"><td style="padding:8px 10px; text-align:left;"><strong>Extra correctieronde</strong><p class="description" style="margin:3px 0 0 0; font-size:11px; color:#666; font-style:italic;">Een extra correctieronde op het ontwerp of de content.</p></td><td style="padding:8px 10px; text-align:center;">1</td><td style="padding:8px 10px; text-align:right;">€&nbsp;90,00</td><td style="padding:8px 10px; text-align:center;">21%</td><td style="padding:8px 10px; text-align:right; font-weight:600;">€&nbsp;90,00</td></tr>
</tbody>
                        </table>
                    </main>
                    <section class="totals-section modern-totals-section">
                        <div class="notes modern-notes">
                            <div class="modern-notes-header">
                                <svg class="notes-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2ZM20 16H5.17L4 17.17V4H20V16Z" fill="currentColor"/>
                                    <path d="M12 14H14V16H16V14H18V12H16V10H14V12H12V14ZM6 12H10V14H6V12Z" fill="currentColor"/>
                                </svg>
                                <h4>Opmerkingen</h4>
                            </div>
                            <p id="preview-opmerkingen"></p>
                        </div>
                        <div class="totals-summary modern-totals-summary">
                            <div class="modern-totals-header">
                                <svg class="totals-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M21 18V19C21 20.1 20.1 21 19 21H5C3.89 21 3 20.1 3 19V5C3 3.9 3.89 3 5 3H19C20.1 3 21 3.9 21 5V6H12C10.89 6 10 6.9 10 8V16C10 17.1 10.89 18 12 18H21ZM12 16H22V8H12V16ZM16 13.5C15.17 13.5 14.5 12.83 14.5 12C14.5 11.17 15.17 10.5 16 10.5C16.83 10.5 17.5 11.17 17.5 12C17.5 12.83 16.83 13.5 16 13.5Z" fill="currentColor"/>
                                </svg>
                                <h4>Totalen</h4>
                            </div>
                            <table class="totals-table modern-totals-table">
                                <tr><td>Subtotaal</td><td id="preview-subtotaal">€ 0,00</td></tr>
                                <tr><td>BTW (21%)</td><td id="preview-btw-21">€ 0,00</td></tr>
                                <tr><td>BTW (9%)</td><td id="preview-btw-9">€ 0,00</td></tr>
                                <tr class="grand-total"><td>TOTAAL</td><td id="preview-totaal">€ 0,00</td></tr>
                            </table>
                        </div>
                    </section>
                    <!-- Nieuwe voorwaarden sectie over de volle breedte -->
                    <div class="voorwaarden-breed verbeterde-voorwaarden" style="max-width: calc(100% - 5mm); margin: 12px 2.5mm 0 2.5mm; background: #f8fafb; border: 1px solid #16a085; border-radius: 7px; box-shadow: 0 1px 4px rgba(22,160,133,0.04); padding: 12px 12px 10px 12px; box-sizing: border-box;">
  <h4 style="font-family: 'Montserrat', sans-serif; color: #16a085; font-size: 1.05rem; font-weight: 700; margin: 0 0 6px 0; letter-spacing: 0.5px; text-transform: uppercase;">Voorwaarden</h4>
  <hr style="border: none; border-top: 1px solid #e0e4ea; margin: 0 0 10px 0;">
  <div style="width:100%;">
    <span id="preview-voorwaarden" style="font-size:12px; color:#34495e; line-height:1.5; display:block;"></span>
  </div>
</div>
                    <section class="acceptance-section modern-acceptance-section">
                        <div class="acceptance-header">
                            <svg class="acceptance-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M18 9L12 15L6 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <h3>Akkoordverklaring</h3>
                        </div>
                        <div class="acceptance-content">
                            <p>Voor akkoord, graag een ondertekende kopie van deze offerte retourneren per e-mail.</p>
                            <div class="signature-area modern-signature-area">
                                <div class="signature-block modern-signature-block">
                                    <span class="signature-label">Naam</span>
                                    <div class="signature-line"></div>
                                </div>
                                <div class="signature-block modern-signature-block">
                                    <span class="signature-label">Datum</span>
                                    <div class="signature-line"></div>
                                </div>
                                <div class="signature-block modern-signature-block">
                                    <span class="signature-label">Handtekening</span>
                                    <div class="signature-line"></div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <footer class="quote-footer modern-footer">
                        <div class="footer-content">
                            <div class="footer-company">
                                <strong id="preview-footer-bedrijfsnaam"></strong>
                            </div>
                            <div class="footer-divider"></div>
                            <div class="footer-website">
                                <span id="preview-footer-website"></span>
                            </div>
                        </div>
                    </footer>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal voor Instellingen -->
    <div id="settings-modal" class="modal">
        <div class="modal-content">
            <span class="close-btn">×</span>
            <h2>Instellingen Bedrijfsgegevens</h2>
            <p>Deze gegevens worden opgeslagen in je browser voor toekomstig gebruik.</p>
            <form id="settings-form" onsubmit="return false;">
                <div class="form-grid">
                    <div class="form-group full-width"><label for="setting-bedrijfsnaam">Bedrijfsnaam</label><input type="text" id="setting-bedrijfsnaam"></div>
                    <div class="form-group full-width"><label for="setting-contactpersoon">Contactpersoon</label><input type="text" id="setting-contactpersoon" placeholder="Naam van contactpersoon"></div>
                    <div class="form-group full-width"><label for="setting-adres">Adres</label><input type="text" id="setting-adres" placeholder="Straatnaam Huisnr, Postcode Stad"></div>
                    <div class="form-group"><label for="setting-telefoon">Telefoonnummer</label><input type="text" id="setting-telefoon" placeholder="+31 6 ********"></div>
                    <div class="form-group"><label for="setting-email">E-mailadres</label><input type="email" id="setting-email"></div>
                    <div class="form-group"><label for="setting-website">Website</label><input type="text" id="setting-website" placeholder="jouwwebsite.nl"></div>
                    <div class="form-group"><label for="setting-iban">IBAN</label><input type="text" id="setting-iban" placeholder="NL00 BANK 0000 0000 00"></div>
                    <div class="form-group"><label for="setting-kvk">KvK-nummer</label><input type="text" id="setting-kvk"></div>
                    <div class="form-group"><label for="setting-btw">BTW-nummer</label><input type="text" id="setting-btw" placeholder="NL000000000B00"></div>
                    <div class="form-group full-width"><label for="setting-logo-upload">Logo uploaden</label><input type="file" id="setting-logo-upload" accept="image/*"></div>
                </div>
                <button type="submit" id="save-settings-btn" style="background: var(--primary-color); color: white; border: none; padding: 12px 25px; border-radius: 5px; cursor: pointer; font-weight: 600; width: 100%; font-size: 1rem; margin-top: 20px;">Opslaan</button>
            </form>
        </div>
    </div>

    <script>
        // Wacht tot de volledige HTML-pagina is geladen voordat het script wordt uitgevoerd.
        window.addEventListener('DOMContentLoaded', function() {
            // --- MODERN ALERT FUNCTIE ---
            function showSaveAlert(msg) {
                const alertDiv = document.getElementById('save-alert');
                alertDiv.innerHTML = `
                  <div style="display:flex;align-items:center;gap:16px;backdrop-filter:blur(2.5px);padding:0 8px;">
                    <span style="display:inline-flex;align-items:center;justify-content:center;width:38px;height:38px;background:linear-gradient(135deg,#16a085 60%,#1abc9c 100%);border-radius:50%;box-shadow:0 2px 8px rgba(22,160,133,0.10);font-size:1.7em;">
                      <svg width='26' height='26' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'><circle cx='12' cy='12' r='12' fill='#fff' opacity='0.18'/><path d='M7 13.5L10.5 17L17 10.5' stroke='#fff' stroke-width='2.2' stroke-linecap='round' stroke-linejoin='round'/></svg>
                    </span>
                    <span style="font-size:1.13em;font-weight:700;letter-spacing:0.2px;line-height:1.2; color:#16a085; text-shadow:0 2px 8px #16a08522;">
                      ${msg}<br><span style='font-size:0.97em;font-weight:400;opacity:0.82;'>Je gegevens zijn veilig opgeslagen.</span>
                    </span>
                  </div>
                `;
                alertDiv.style.display = 'block';
                alertDiv.style.background = 'linear-gradient(120deg, #e0f7f4 0%, #fff 100%)';
                alertDiv.style.border = '1.5px solid #16a08533';
                alertDiv.style.color = '#16a085';
                alertDiv.style.padding = '15px 32px 15px 18px';
                alertDiv.style.borderRadius = '16px';
                alertDiv.style.fontWeight = '500';
                alertDiv.style.boxShadow = '0 8px 32px rgba(22,160,133,0.13), 0 1.5px 0 #16a08522';
                alertDiv.style.fontSize = '1.13rem';
                alertDiv.style.letterSpacing = '0.5px';
                alertDiv.style.transition = 'opacity 0.5s';
                alertDiv.style.opacity = '1';
                alertDiv.style.textAlign = 'left';
                alertDiv.style.pointerEvents = 'none';
                setTimeout(() => {
                  alertDiv.style.opacity = '0';
                  setTimeout(() => { alertDiv.style.display = 'none'; alertDiv.style.opacity = '1'; }, 800);
                }, 2200);
            }

            // --- HULPFUNCTIES ---
            // Standaard voorwaarden tekst (compact, kernpunten vet)
            const standaardVoorwaarden = "<strong>Deze offerte is 30 dagen geldig.</strong> Een <strong>aanbetaling van 30%</strong> is essentieel voor planning en materialen; <strong>restbetaling dient binnen 14 dagen</strong>. Meerwerk (<strong>€75,-/uur excl. btw + materialen</strong>) uitsluitend na uw <strong>schriftelijke goedkeuring</strong>. Projectduur is flexibel in overleg. U krijgt <strong>3 maanden garantie</strong> op ons werk; op materialen geldt de <strong>fabrieksgarantie</strong>. Annuleren is kosteloos tot <strong>7 werkdagen vóór start</strong>, daarna wordt de aanbetaling ingehouden. Akkoord met deze offerte houdt tevens akkoord met onze <strong>Algemene Voorwaarden</strong> in. <strong>Wij streven naar een uitstekend eindresultaat.</strong>";
            // --- AANVULLENDE INFO (OPMERKINGEN & VOORWAARDEN) OPSLAAN ---
            var opmerkingenInput = document.getElementById('opmerkingen');
            var voorwaardenInput = document.getElementById('voorwaarden');
            var previewVoorwaarden = document.getElementById('preview-voorwaarden');
            var previewOpmerkingen = document.getElementById('preview-opmerkingen');
            var saveAanvullendeBtn = document.getElementById('save-aanvullende-btn');
            // Laad opgeslagen info of standaardtekst
            function loadAanvullendeInfo() {
                const opgeslagenVoorwaarden = localStorage.getItem('offerteVoorwaarden');
                const opgeslagenOpmerkingen = localStorage.getItem('offerteOpmerkingen');
                if (opgeslagenVoorwaarden && opgeslagenVoorwaarden.trim() !== '') {
                    voorwaardenInput.value = opgeslagenVoorwaarden;
                    previewVoorwaarden.innerHTML = opgeslagenVoorwaarden;
                } else {
                    voorwaardenInput.value = standaardVoorwaarden;
                    previewVoorwaarden.innerHTML = standaardVoorwaarden;
                }
                if (opgeslagenOpmerkingen && opgeslagenOpmerkingen.trim() !== '') {
                    opmerkingenInput.value = opgeslagenOpmerkingen;
                    previewOpmerkingen.innerHTML = opgeslagenOpmerkingen.replace(/\n/g, '<br>');
                }
            }
            // Synchroniseer live bij typen
            if (voorwaardenInput && previewVoorwaarden) {
                voorwaardenInput.addEventListener('input', function() {
                    previewVoorwaarden.innerHTML = voorwaardenInput.value;
                });
            }
            if (opmerkingenInput && previewOpmerkingen) {
                opmerkingenInput.addEventListener('input', function() {
                    previewOpmerkingen.innerHTML = opmerkingenInput.value.replace(/\n/g, '<br>');
                });
            }
            if (saveAanvullendeBtn) {
                saveAanvullendeBtn.addEventListener('click', function() {
                    localStorage.setItem('offerteVoorwaarden', voorwaardenInput.value);
                    localStorage.setItem('offerteOpmerkingen', opmerkingenInput.value);
                    previewVoorwaarden.innerHTML = voorwaardenInput.value;
                    previewOpmerkingen.innerHTML = opmerkingenInput.value.replace(/\n/g, '<br>');
                    showSaveAlert('Opgeslagen!');
                });
            }
            const formatCurrency = value => new Intl.NumberFormat('nl-NL', { style: 'currency', currency: 'EUR' }).format(value);
            const formatDate = dateString => {
                if (!dateString) return '';
                const [year, month, day] = dateString.split('-');
                return `${day}-${month}-${year}`;
            };

            // --- INSTELLINGEN (LOCALSTORAGE) ---
            const settingsModal = document.getElementById('settings-modal');
            const defaultSettings = {
                bedrijfsnaam: 'A.S.Allround klussen',
                contactpersoon: 'Contactpersoon',
                adres: 'Kelspelstraat 3, 3641 JS Mijdrecht',
                telefoon: '+316-********',
                email: '<EMAIL>',
                website: 'jouwwebsite.nl',
                iban: '******************',
                kvk: '[Jouw KvK-nr]',
                btw: 'NL002490282B64',
                logo: 'https://via.placeholder.com/150x50.png?text=Logo'
            };

            const saveSettings = () => {
                const logoUpload = document.getElementById('setting-logo-upload');
                let logoData = localStorage.getItem('uploadedLogo') || '';
                if (logoUpload && logoUpload.files && logoUpload.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        logoData = e.target.result;
                        localStorage.setItem('uploadedLogo', logoData);
                        saveAllSettings(logoData);
                    };
                    reader.readAsDataURL(logoUpload.files[0]);
                    return;
                }
                saveAllSettings(logoData);
            };

            function saveAllSettings(logoData) {
                const settings = {
                    bedrijfsnaam: document.getElementById('setting-bedrijfsnaam').value,
                    contactpersoon: document.getElementById('setting-contactpersoon').value,
                    adres: document.getElementById('setting-adres').value,
                    telefoon: document.getElementById('setting-telefoon').value,
                    email: document.getElementById('setting-email').value,
                    website: document.getElementById('setting-website').value,
                    iban: document.getElementById('setting-iban').value,
                    kvk: document.getElementById('setting-kvk').value,
                    btw: document.getElementById('setting-btw').value,
                    logo: logoData
                };
                localStorage.setItem('quoteGeneratorSettings', JSON.stringify(settings));
                loadSettings(); // Herlaad instellingen om de preview bij te werken
                document.getElementById('settings-modal').style.display = 'none';
            }

            const loadSettings = () => {
                const savedSettings = JSON.parse(localStorage.getItem('quoteGeneratorSettings')) || defaultSettings;
                Object.keys(savedSettings).forEach(key => {
                    const el = document.getElementById(`setting-${key.replace('_', '-')}`);
                    if (el) el.value = savedSettings[key];
                });
                updateCompanyInfo(savedSettings);
            };

            const updateCompanyInfo = (settings) => {
                document.getElementById('preview-verzender-info').innerHTML = `
                  <div class="modern-sender-grid">
                    <div class="sender-company-name">${settings.bedrijfsnaam || 'A.S.Allround klussen'}</div>
                    <div class="sender-contact-person">${settings.contactpersoon || 'Contactpersoon'}</div>
                    <div class="sender-address">${settings.adres || 'Kelspelstraat 3, 3641 JS Mijdrecht'}</div>

                    <div class="sender-info-grid">
                      <div class="sender-info-item">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="info-icon">
                          <path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z"/>
                        </svg>
                        <span class="info-label">Tel:</span>
                        <span class="info-value">${settings.telefoon || '+316-********'}</span>
                      </div>

                      <div class="sender-info-item">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="info-icon">
                          <path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z"/>
                        </svg>
                        <span class="info-label">E-mail:</span>
                        <a href="mailto:${settings.email || '<EMAIL>'}" class="contact-link info-value">${settings.email || '<EMAIL>'}</a>
                      </div>

                      <div class="sender-info-item">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="info-icon">
                          <path d="M16.36,14C16.44,13.3 16.5,12.66 16.5,12S16.44,10.7 16.36,10H19.74C19.9,10.64 20,11.31 20,12S19.9,13.36 19.74,14M14.59,19.56C15.19,18.45 15.65,17.25 15.97,16H18.92C17.96,17.65 16.43,18.93 14.59,19.56M14.34,14H9.66C9.56,13.32 9.5,12.66 9.5,12S9.56,10.68 9.66,10H14.34C14.43,10.68 14.5,11.32 14.5,12S14.43,13.32 14.34,14M12,19.96C11.17,18.76 10.5,17.43 10.09,16H13.91C13.5,17.43 12.83,18.76 12,19.96M8,8H5.08C6.03,6.34 7.57,5.06 9.4,4.44C8.8,5.55 8.35,6.75 8,8M5.08,16H8C8.35,17.25 8.8,18.45 9.4,19.56C7.57,18.93 6.03,17.65 5.08,16M4.26,14C4.1,13.36 4,12.69 4,12S4.1,10.64 4.26,10H7.64C7.56,10.7 7.5,11.34 7.5,12S7.56,13.3 7.64,14M12,4.03C12.83,5.23 13.5,6.57 13.91,8H10.09C10.5,6.57 11.17,5.23 12,4.03M18.92,8H15.97C15.65,6.75 15.19,5.55 14.59,4.44C16.43,5.07 17.96,6.34 18.92,8M12,2C6.47,2 2,6.5 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                        </svg>
                        <span class="info-label">Website:</span>
                        <a href="${(settings.website && !settings.website.startsWith('http') ? 'https://' + settings.website : (settings.website || 'https://www.ASklussen.nl'))}" class="contact-link info-value">${settings.website || 'www.ASklussen.nl'}</a>
                      </div>

                      <div class="sender-info-item">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="info-icon">
                          <path d="M11.5,1L2,6V8H21V6M16,10V17H19V19H2V17H5V10H7V17H9V10H11V17H13V10H15V17H16M8,10V17H10V10"/>
                        </svg>
                        <span class="info-label">IBAN:</span>
                        <span class="info-value">${settings.iban || '******************'}</span>
                      </div>

                      ${settings.kvk ? `
                      <div class="sender-info-item">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="info-icon">
                          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        <span class="info-label">KvK:</span>
                        <span class="info-value">${settings.kvk}</span>
                      </div>
                      ` : ''}

                      ${settings.btw ? `
                      <div class="sender-info-item">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="info-icon">
                          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        <span class="info-label">BTW:</span>
                        <span class="info-value">${settings.btw}</span>
                      </div>
                      ` : ''}
                    </div>
                  </div>
                `;
                document.getElementById('preview-footer-bedrijfsnaam').textContent = settings.bedrijfsnaam;
                document.getElementById('preview-footer-website').textContent = settings.website;
                document.getElementById('preview-logo').src = settings.logo || defaultSettings.logo;
            };

            // Event listeners voor de instellingen-modal
            document.getElementById('settings-btn').addEventListener('click', () => settingsModal.style.display = 'block');
            document.querySelector('.close-btn').addEventListener('click', () => settingsModal.style.display = 'none');
            window.addEventListener('click', (e) => {
                if (e.target == settingsModal) settingsModal.style.display = 'none';
            });
            document.getElementById('settings-form').addEventListener('submit', (e) => {
                e.preventDefault();
                saveSettings();
            });
            
            // --- BEHEER VAN OFFERTEREGELS ---
            const itemsFormContainer = document.getElementById('items-form-container');
            const itemsPreviewBody = document.getElementById('preview-items-body');
            let itemId = 0;

            const updateTotals = () => {
                let subtotal = 0;
                let btw21 = 0;
                let btw9 = 0;
                itemsPreviewBody.innerHTML = ''; // Leeg de preview tabel
                
                itemsFormContainer.querySelectorAll('.item-row').forEach(row => {
                    const description = row.querySelector('.item-description').value;
                    const longDescription = row.querySelector('.item-long-description').value;
                    const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
                    const price = parseFloat(row.querySelector('.item-price').value) || 0;
                    const btwPercentage = parseFloat(row.querySelector('.item-btw')?.value || '21');
                    const total = quantity * price;
                    subtotal += total;
                    if (btwPercentage === 21) {
                        btw21 += total * 0.21;
                    } else if (btwPercentage === 9) {
                        btw9 += total * 0.09;
                    }

                    // Voeg de regel toe aan de preview tabel
                    const previewRow = document.createElement('tr');
                    previewRow.innerHTML = `<td><strong>${description || '...'}</strong><p class="description">${longDescription.replace(/\n/g, '<br>')}</p></td><td>${quantity}</td><td>${formatCurrency(price)}</td><td>${btwPercentage}%</td><td>${formatCurrency(total)}</td>`;
                    itemsPreviewBody.appendChild(previewRow);
                });

                // Bereken en update totalen
                const total = subtotal + btw21 + btw9;
                document.getElementById('preview-subtotaal').textContent = formatCurrency(subtotal);
                document.getElementById('preview-btw-21').textContent = formatCurrency(btw21);
                document.getElementById('preview-btw-9').textContent = formatCurrency(btw9);
                document.getElementById('preview-totaal').textContent = formatCurrency(total);
            };

            const addItemRow = (desc = '', longDesc = '', qty = 1, price = 0, btwTarief = 21) => {
                itemId++;
                const row = document.createElement('div');
                row.className = 'item-row';
                row.innerHTML = `
                    <div class="item-row-header">
                        <strong>Regel #${itemId}</strong>
                        <button type="button" class="remove-item-btn" title="Verwijder regel">×</button>
                    </div>
                    <div class="form-group full-width">
                        <label for="item-desc-${itemId}">Korte Omschrijving</label>
                        <input type="text" class="item-description" id="item-desc-${itemId}" value="${desc}">
                    </div>
                    <div class="form-group full-width">
                        <label for="item-btw-${itemId}">BTW-tarief</label>
                        <select class="item-btw" id="item-btw-${itemId}">
                            <option value="21"${btwTarief == 21 ? ' selected' : ''}>21%</option>
                            <option value="9"${btwTarief == 9 ? ' selected' : ''}>9%</option>
                        </select>
                    </div>
                    <div class="form-group full-width">
                        <label for="item-long-desc-${itemId}">Lange Omschrijving</label>
                        <textarea class="item-long-description" id="item-long-desc-${itemId}">${longDesc}</textarea>
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="item-qty-${itemId}">Aantal</label>
                            <input type="number" class="item-quantity" id="item-qty-${itemId}" value="${qty}" step="any">
                        </div>
                        <div class="form-group">
                            <label for="item-price-${itemId}">Prijs p/s</label>
                            <input type="number" class="item-price" id="item-price-${itemId}" value="${price}" step="0.01">
                        </div>
                    </div>`;
                itemsFormContainer.appendChild(row);

                // Voeg een event listener toe aan de verwijderknop
                row.querySelector('.remove-item-btn').addEventListener('click', () => {
                    row.remove();
                    updateTotals(); // Werk de totalen bij na verwijderen
                });

                // Update preview direct bij wijzigen van btw-tarief
                row.querySelector('.item-btw').addEventListener('change', updateTotals);
            };

            document.getElementById('add-item-btn').addEventListener('click', () => addItemRow());

            // --- SYNCHRONISATIE TUSSEN FORMULIER EN PREVIEW ---
            const syncInput = (e) => {
                const el = e.target;
                const targetSelector = el.dataset.target;

                if (targetSelector) { // Voor normale velden
                    const targetElement = document.querySelector(targetSelector);
                    if (!targetElement) return;

                    if (el.type === 'date') {
                        targetElement.textContent = formatDate(el.value);
                    } else if (el.tagName === 'TEXTAREA') {
                        targetElement.innerHTML = el.value.replace(/\n/g, '<br>');
                    } else {
                        targetElement.textContent = el.value;
                    }
                } else if (el.dataset.targetList) { // Voor velden die een lijst moeten worden
                    const targetListElement = document.querySelector(el.dataset.targetList);
                    if (!targetListElement) return;

                    const items = el.value.split('\n').filter(item => item.trim() !== '');
                    targetListElement.innerHTML = items.map(item => `<li>${item}</li>`).join('');
                } else if (el.closest('.item-row')) { // Als een veld in een offerteregel wordt aangepast
                    updateTotals();
                }
            };

            var quoteForm = document.getElementById('quote-form');
            if (quoteForm) {
                quoteForm.addEventListener('input', syncInput);
            }
            
            // --- PDF GENERATIE ---
            var pdfBtn = document.getElementById('generate-pdf');
            if (pdfBtn) {
                pdfBtn.addEventListener('click', function() {
                    window.print();
                });
            }


            // --- INITIALISATIE ---
            function init() {
                // Stel standaard datums in
                // Laad aanvullende info velden en preview
                loadAanvullendeInfo();
                const today = new Date();
                const validUntil = new Date();
                validUntil.setDate(today.getDate() + 30);
                var datumInput = document.getElementById('datum');
                var geldigTotInput = document.getElementById('geldig-tot');
                if (datumInput) datumInput.value = today.toISOString().split('T')[0];
                if (geldigTotInput) geldigTotInput.value = validUntil.toISOString().split('T')[0];

                // Laad opgeslagen bedrijfsinstellingen
                loadSettings();

                // Voeg standaard offerteregels toe voor demonstratie
                addItemRow('Strategie & Conceptontwikkeling', 'Uitwerken van visuele identiteit en projectstrategie.', 1, 850, 21);
                addItemRow('Webdesign & Realisatie', 'Ontwerp en bouw van een maatwerk WordPress website.', 1, 3200, 21);
                addItemRow('Service & Hosting (1 jaar)', 'Hosting, onderhoud, updates en support.', 1, 450, 21);
                addItemRow('SEO Optimalisatie', 'Basis zoekmachineoptimalisatie voor betere vindbaarheid.', 1, 300, 21);
                addItemRow('Content Integratie', 'Plaatsen en structureren van aangeleverde teksten en afbeeldingen.', 1, 200, 21);
                addItemRow('Training & Instructie', 'Uitleg en training voor het gebruik van het CMS.', 1, 150, 21);
                addItemRow('Supportpakket (6 maanden)', 'Telefonische en e-mail support na oplevering.', 1, 180, 21);
                addItemRow('SSL Certificaat', 'Beveiligde verbinding voor de website.', 1, 75, 21);
                addItemRow('Back-up Service (1 jaar)', 'Automatische back-ups van de website.', 1, 120, 21);
                addItemRow('Extra correctieronde', 'Een extra correctieronde op het ontwerp of de content.', 1, 90, 21);

                // Roep voor alle velden de 'input' event aan om de preview te vullen
                var allInputs = document.querySelectorAll('#quote-form input, #quote-form textarea');
                allInputs.forEach(function(el) {
                    el.dispatchEvent(new Event('input', { bubbles: true }));
                });
                
                // Werk de totalen de eerste keer bij
                updateTotals();
            };

            // Start de applicatie
            init();
        });
    </script>
</body>
</html>